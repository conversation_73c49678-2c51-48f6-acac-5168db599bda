export default [
  {
    title: '条形图',
    schema: {
      componentName: 'BarChart',
      props: {
        data: [
          { platform: '京东', percentage: 142.01 },
          { platform: '抖音', percentage: 83.65 },
          { platform: '淘系', percentage: 78.29 },
          { platform: '拼多多', percentage: 76.17 },
          { platform: '快手', percentage: 42.05 },
          { platform: '唯品会', percentage: 33.56 },
          { platform: '小红书', percentage: 9.61 },
        ],
        xField: 'percentage',
        yField: 'platform',
        color: '#4285f4',
        height: 400,
        showLabel: true,
        labelPosition: 'right',
        labelFormatter: (value: number) => `${value}%`,
        marginRatio: 0.3,
        showGrid: true,
        sort: true,
        sortReverse: true,
      },
    },
  },
];
