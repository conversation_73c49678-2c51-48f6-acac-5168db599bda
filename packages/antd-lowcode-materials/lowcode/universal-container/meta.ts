import props from './props';

const UniversalContainerSnippets = {
  title: '通用型容器',
  content: {
    type: 'JSSlot', // 可支持拖拽组件或可支持外部传入组件，此项需配置为 JSSlot
    id: 'meelgeek_materials_components_ki15x1',
    iconSize: 32,
  },
};

const UniversalContainerMeta = [
  {
    componentName: 'UniversalContainer',
    title: '通用型容器',
    category: '驾驶舱',
    group: 'MelGeek组件',
    configure: {
      component: {
        isContainer: false,
        nestingRule: {},
      },
      props,
    },
    snippets: [
      {
        title: '通用型容器',
        schema: {
          componentName: 'UniversalContainer',
          props: UniversalContainerSnippets,
        },
      },
    ],
  },
];

export default UniversalContainerMeta;
