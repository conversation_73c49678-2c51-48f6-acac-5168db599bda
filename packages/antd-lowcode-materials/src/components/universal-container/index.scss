.universal-container {
  position: relative;
  width: 100%;
  padding: 16px;
  border-radius: 16px;
  background: #ffffff;

  .header {
    display: flex;
    line-height: 30px;
    margin-bottom: 12px;
    align-items: center;
    justify-content: space-between;
    .anticon {
      margin-right: 10px;
    }
  }

  .header-title {
    color: #1d2129;
      font-family: number-mm, font-fz-bold;
      font-size: 16px;
      
      display: flex;
      flex-direction: row;
      align-items: center;

      .header-icon {
        width: 32px;
        height: 32px;

        margin-inline-end: 4px;
      }
  }
}