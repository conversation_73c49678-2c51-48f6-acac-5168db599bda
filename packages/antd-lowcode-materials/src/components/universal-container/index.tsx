import React from 'react';
import './index.scss';

import { UniversalContainerProps } from './types';
import { IconFont } from '../base';

const UniversalContainer = (props: UniversalContainerProps) => {
  const {
    title,
    icon,
    content,
    style,
  } = props;

  return (
    <div className="universal-container">
        <div className="header">
          <div
            className="header-title"
          >
            {icon ? <IconFont type={icon} style={{ fontSize: 32 }} /> : null}
            {title}
          </div>
        </div>
      <div style={style}>{content}</div>
    </div>
  )
};

export default UniversalContainer;
