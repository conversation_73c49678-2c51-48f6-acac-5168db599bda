.bar-chart-container {
  width: 100%;
  
  // 图表容器样式优化
  .g2-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  // 条形样式优化
  .g2-element {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  // 坐标轴样式
  .g2-axis {
    .g2-axis-line {
      stroke: #e8e8e8;
      stroke-width: 1;
    }
    
    .g2-axis-tick {
      stroke: #e8e8e8;
      stroke-width: 1;
    }
    
    .g2-axis-label {
      fill: #666;
      font-size: 12px;
    }
  }
  
  // 网格线样式
  .g2-grid {
    .g2-grid-line {
      stroke: #f0f0f0;
      stroke-width: 1;
      stroke-dasharray: 2, 2;
    }
  }
  
  // 标签样式
  .g2-label {
    font-size: 12px;
    font-weight: normal;
  }
  
  // 工具提示样式
  .g2-tooltip {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}
