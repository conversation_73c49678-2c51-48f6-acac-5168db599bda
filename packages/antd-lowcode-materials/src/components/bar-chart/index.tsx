import React from 'react';
import cls from 'classnames';
import { Bar } from '@ant-design/plots';

import './index.scss';

interface BarChartProps {
  /** 图表数据 */
  data?: any[];
  /** X轴字段名 */
  xField?: string;
  /** Y轴字段名 */
  yField?: string;
  /** 条形颜色 */
  color?: string;
  /** 图表高度 */
  height?: number;
  /** 图表内边距 */
  padding?: number | number[];
  /** 是否显示标签 */
  showLabel?: boolean;
  /** 标签位置 */
  labelPosition?: 'left' | 'middle' | 'right';
  /** 标签格式化函数 */
  labelFormatter?: string | ((value: any) => string);
  /** 是否排序 */
  sort?: boolean;
  /** 是否倒序排列 */
  sortReverse?: boolean;
  /** 条形间距比例 */
  marginRatio?: number;
  /** 是否显示网格线 */
  showGrid?: boolean;
  /** 容器样式 */
  style?: React.CSSProperties;
  /** 容器类名 */
  className?: string;
  /** 图表配置的其他属性 */
  [key: string]: any;
}

const BarChart: React.FC<BarChartProps> = ({
  data = [],
  xField = 'value',
  yField = 'category',
  color = '#1890ff',
  height = 400,
  padding = 'auto',
  showLabel = false,
  labelPosition = 'right',
  labelFormatter,
  sort = false,
  sortReverse = false,
  marginRatio = 0.3,
  showGrid = true,
  style,
  className,
}) => {
  // 构建图表配置
  const config = {
    data,
    xField,
    yField,
    height,
    padding,
    color,
    marginRatio,
    // 排序配置
    ...(sort && {
      sort: {
        reverse: sortReverse,
      },
    }),
    // 标签配置
    ...(showLabel && {
      label: {
        text: yField,
        position: labelPosition,
        ...(labelFormatter && {
          formatter: labelFormatter,
        }),
        style: {
          textAlign: labelPosition === 'right' ? 'start' : labelPosition === 'left' ? 'end' : 'center',
          fill: labelPosition === 'middle' ? '#fff' : '#000',
          fontSize: 12,
          fontWeight: 'normal',
        },
      },
    }),
    // 坐标轴配置
    axis: {
      x: {
        grid: showGrid,
        line: true,
        tick: true,
        min: 0, // 确保X轴从0开始
        label: {
          formatter: (value: number) => `${value}%`,
          style: {
            fontSize: 12,
            fill: '#666',
          },
        },
      },
      y: {
        grid: false,
        line: true,
        tick: true,
        label: {
          style: {
            fontSize: 12,
            fill: '#666',
          },
        },
      },
    },
    // 确保条形从0开始
    scale: {
      [xField]: {
        min: 0,
        nice: true,
      },
    },
    // 交互配置
    interaction: {
      elementHighlight: true,
    },
    // 动画配置
    animation: {
      appear: {
        animation: 'wave-in',
        duration: 1000,
      },
    },
    // 主题配置
    theme: {
      geometries: {
        interval: {
          rect: {
            default: {
              style: {
                radius: [0, 2, 2, 0], // 圆角
              },
            },
          },
        },
      },
    },
  };

  return (
    <div className={cls('bar-chart-container', className)} style={style}>
      <Bar {...config} />
    </div>
  );
};

export default BarChart;
export { BarChart };
